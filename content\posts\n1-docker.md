---
title: "使用 Docker 部署 OpenWrt 软路由及解决宿主机通信问题"
date: 2020-11-15T16:09:10+08:00
draft: false
tags: [ "n1","docker-compose","docker","openwrt"]
tags_weight: 88
categories: ["fix","openwrt"]
categories_weight: 88
keywords:
- 软路由
- docker
- openwrt
- fix
description: "使用 Docker 部署 OpenWrt 软路由及解决宿主机通信问题"
---

# 环境配置

- **宿主机 IP**：************2
- **OpenWrt 容器 IP**：************* (macvlan)
- **主路由网关**：***********
- **网络段**：***********/24
- **Docker 镜像**：`raymondwong/openwrt_r9:21.2.1-arm64`
- **操作系统**：ARMBIAN

# 部署 OpenWrt 软路由

## 安装 Docker Compose

```bash
apt install -y docker-compose # CentOS 系统请使用 yum
```

## 创建 Docker Compose 配置文件

> Docker 镜像地址：[raymondwong/openwrt_r9](https://hub.docker.com/r/raymondwong/openwrt_r9/tags?page=1&ordering=last_updated)

```bash
mkdir -p /data/docker-compose/openwrt/

cd /data/docker-compose/openwrt/

ip a  # 查看需要绑定的网卡名称

cat > docker-compose.yaml  << EOF
version: '2'

services:
  openwrt:
    image:  raymondwong/openwrt_r9:21.2.1-arm64  # ARM 架构镜像，x86 架构需要更换对应镜像
    container_name: openwrt_r9
    privileged: true
    restart: always
    networks:
      openwrt_macnet:
        ipv4_address: *************

networks:
  openwrt_macnet:
    driver: macvlan
    driver_opts:
      parent: eth0 # 桥接的网卡名称
    ipam:
      config:
        - subnet: ***********/24
          ip_range: *************/25
          gateway: ***********
EOF
```

## 启动 OpenWrt 容器

### 启用网卡混杂模式

```bash
ip link set eth0 promisc on
```

### 启动容器

```bash
docker-compose up -d
```

## 配置 OpenWrt 网络

> 容器启动后，默认 IP 地址为 `*************`，与当前局域网网段不符，需要修改 IP 地址和网关地址。

```bash
docker exec -it openwrt_r9 bash -c "sed -i 's#*************#*************#g;s#***********#***********#g' /etc/config/network" \
&& docker restart openwrt_r9

docker exec -it openwrt_r9 bash -c "ping -c 3 baidu.com"  # 测试网络连通性
```

![image-20210527102828331](https://cdn.treesir.pub/img/image-20210527102828331.png)

> 默认登录账号：`root:password`

![image-20210527103005072](https://cdn.treesir.pub/img/image-20210527103005072.png)

# 解决容器与宿主机通信问题

## 问题原因及解决方案

使用 Docker 的 `macvlan` 模式部署 OpenWrt 时会遇到通信问题。macvlan 模式在一张物理网卡上虚拟出多个虚拟网卡，每个网卡具有不同的 MAC 地址，可以让宿主机和容器同时接入网络并使用不同的 IP 地址。

**问题**：macvlan 模式出于安全考虑，禁止了宿主机与容器的直接通信，即使它们在同一网段内也无法互相 ping 通。

**解决方案**：在宿主机上创建一个新的 macvlan 接口，然后修改路由，使数据通过该 macvlan 传输到容器内的 macvlan。macvlan 接口之间是可以互相通信的。

## 配置步骤

> **以下操作在宿主机上执行**

### 创建 macvlan 接口

创建一个名为 mynet 的 macvlan 接口（注意不要与容器的 macvlan 网卡重名）：

```shell
ip link add mynet link eth0 type macvlan mode bridge
```

### 配置接口 IP 并启用

```shell
ip addr add ************ dev mynet
ip link set mynet up
```

### 添加静态路由

使宿主机与 OpenWrt 的通信通过 mynet 接口进行：

```shell
ip route add ************* dev mynet
```

### 测试连通性

```shell
docker exec -it openwrt_r9 ping ************2 -c 3
PING ************2 (************2): 56 data bytes
64 bytes from ************2: seq=0 ttl=64 time=0.508 ms
```

### 配置开机自启

```shell
cat >> /etc/rc.local << EOF
ip link add mynet link eth0 type macvlan mode bridge
ip addr add ************ dev mynet
ip link set mynet up
ip route add ************* dev mynet
EOF
```
![image-20210527103450010](https://cdn.treesir.pub/img/image-20210527103450010.png)

### 设置脚本执行权限

确保开机自启脚本具有可执行权限：

```shell
chmod a+x /etc/rc.local
```

# 参考文档

- [Using Docker macvlan networks](https://blog.oddbit.com/post/2018-03-12-using-docker-macvlan-networks/)

